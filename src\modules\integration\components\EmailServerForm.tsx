import React, { useState, useRef, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import {
  Card,
  Typography,
  Button,
  Input,
  Textarea,
  FormItem,
  Form,
  Icon,
  Alert,
  ResponsiveGrid,
  Chip,
} from '@/shared/components/common';
import Toggle from '@/shared/components/common/Toggle';
import { emailServerConfigurationSchema } from '../email/schemas';
import {
  EmailServerConfiguration,
  TestEmailServerWithConfigDto,
  EmailServerConfigDto,
} from '../email/types';
import { useTestEmailServer, useTestEmailServerWithConfig } from '../email/hooks';
import { useEmailProviders } from '../email/hooks/useProviders';
import { EmailProvider } from '../email/types/providers';
import ProviderCard from '../email/components/provider/ProviderCard';

// Định nghĩa kiểu dữ liệu cho form
export type EmailServerFormValues = z.infer<typeof emailServerConfigurationSchema>;

interface EmailServerFormProps {
  /**
   * Dữ liệu ban đầu cho form (khi chỉnh sửa)
   */
  initialData?: EmailServerConfiguration | null;

  /**
   * Hàm xử lý khi submit form
   */
  onSubmit: (values: Record<string, unknown>) => void;

  /**
   * Hàm xử lý khi hủy form
   */
  onCancel: () => void;

  /**
   * Trạng thái đang submit form
   */
  isSubmitting?: boolean;

  /**
   * Chế độ chỉ đọc
   */
  readOnly?: boolean;
}

/**
 * Form tạo/chỉnh sửa Email Server Configuration
 */
const EmailServerForm: React.FC<EmailServerFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isSubmitting = false,
  readOnly = false,
}) => {
  const { t } = useTranslation(['user', 'common', 'integration']);
  const formRef = useRef(null);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);

  const testEmailMutation = useTestEmailServer();
  const testEmailWithConfigMutation = useTestEmailServerWithConfig();
  const isEditMode = !!initialData;

  // Provider selection states
  const [selectedProvider, setSelectedProvider] = useState<EmailProvider | null>(null);
  const [showProviderSelection, setShowProviderSelection] = useState(!isEditMode);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<
    'all' | 'personal' | 'business' | 'transactional'
  >('all');

  // Test connection states
  const [testEmail, setTestEmail] = useState('');

  // Load providers
  const { data: providers = [], isLoading: isLoadingProviders } = useEmailProviders();

  // Filter providers based on search and category
  const filteredProviders = useMemo(() => {
    return providers.filter(provider => {
      const matchesSearch =
        !searchQuery ||
        provider.displayName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        provider.description.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesCategory = selectedCategory === 'all' || provider.category === selectedCategory;

      return matchesSearch && matchesCategory;
    });
  }, [providers, searchQuery, selectedCategory]);

  // Handle provider selection
  const handleProviderSelect = useCallback((provider: EmailProvider) => {
    setSelectedProvider(provider);
    setShowProviderSelection(false);

    // Auto-fill form with provider defaults
    if (formRef.current) {
      const form = formRef.current as { setValue: (name: string, value: unknown) => void };
      form.setValue('host', provider.defaultConfig.host);
      form.setValue('port', provider.defaultConfig.port);
      form.setValue('useSsl', provider.defaultConfig.useSsl);
      form.setValue('useStartTls', provider.defaultConfig.useStartTls);
    }
  }, []);

  // Handle back to provider selection
  const handleBackToProviderSelection = useCallback(() => {
    setSelectedProvider(null);
    setShowProviderSelection(true);
  }, []);

  // Validation email helper
  const isValidEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Get provider-specific form fields
  const getProviderSpecificFields = useCallback(() => {
    if (!selectedProvider) return null;

    const commonFields = (
      <>
        <FormItem
          name="username"
          label={t('admin:integration.email.form.fields.username')}
          required
        >
          <Input
            type="email"
            placeholder={
              selectedProvider.id === 'gmail'
                ? '<EMAIL>'
                : selectedProvider.id === 'outlook'
                  ? '<EMAIL>'
                  : t('admin:integration.email.form.placeholders.username')
            }
            disabled={readOnly || isSubmitting}
            fullWidth
          />
        </FormItem>

        <FormItem
          name="password"
          label={
            selectedProvider.authMethods.includes('oauth')
              ? t('integration:provider.appPassword', 'App Password')
              : t('admin:integration.email.form.fields.password')
          }
          required={!isEditMode}
        >
          <Input
            type="password"
            placeholder={
              selectedProvider.authMethods.includes('oauth')
                ? t(
                    'integration:provider.appPasswordPlaceholder',
                    'Nhập App Password từ cài đặt bảo mật'
                  )
                : t('admin:integration.email.form.placeholders.password')
            }
            disabled={readOnly || isSubmitting}
            fullWidth
          />
        </FormItem>
      </>
    );

    // Provider-specific additional fields
    switch (selectedProvider.id) {
      case 'gmail':
        return (
          <>
            {commonFields}
            {selectedProvider.authMethods.includes('oauth') && (
              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <Typography variant="body2" className="text-blue-700 dark:text-blue-300">
                  <Icon name="info" className="w-4 h-4 inline mr-2" />
                  {t(
                    'integration:provider.gmail.appPasswordInfo',
                    'Để sử dụng Gmail, bạn cần tạo App Password trong cài đặt bảo mật Google.'
                  )}
                </Typography>
              </div>
            )}
          </>
        );

      case 'outlook':
        return (
          <>
            {commonFields}
            <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <Typography variant="body2" className="text-blue-700 dark:text-blue-300">
                <Icon name="info" className="w-4 h-4 inline mr-2" />
                {t(
                  'integration:provider.outlook.info',
                  'Outlook hỗ trợ cả tài khoản cá nhân và doanh nghiệp.'
                )}
              </Typography>
            </div>
          </>
        );

      case 'amazon-ses':
        return (
          <>
            <FormItem
              name="username"
              label={t('integration:provider.ses.accessKey', 'AWS Access Key ID')}
              required
            >
              <Input placeholder="AKIA..." disabled={readOnly || isSubmitting} fullWidth />
            </FormItem>
            <FormItem
              name="password"
              label={t('integration:provider.ses.secretKey', 'AWS Secret Access Key')}
              required={!isEditMode}
            >
              <Input
                type="password"
                placeholder="Enter AWS Secret Access Key"
                disabled={readOnly || isSubmitting}
                fullWidth
              />
            </FormItem>
            <FormItem
              name="region"
              label={t('integration:provider.ses.region', 'AWS Region')}
              required
            >
              <Input placeholder="us-east-1" disabled={readOnly || isSubmitting} fullWidth />
            </FormItem>
          </>
        );

      default:
        return commonFields;
    }
  }, [selectedProvider, t, readOnly, isSubmitting, isEditMode]);

  // Memoize defaultValues để tránh re-render không cần thiết
  const defaultValues = useMemo(() => {
    console.log('🔄 Calculating defaultValues for EmailServerForm', { initialData });
    return initialData
      ? {
          serverName: initialData.serverName,
          host: initialData.host,
          port: initialData.port,
          username: initialData.username,
          password: '', // Không hiển thị password cũ
          useSsl: initialData.useSsl,
          useStartTls: initialData.useStartTls,
          additionalSettings: initialData.additionalSettings
            ? JSON.stringify(initialData.additionalSettings, null, 2)
            : '',
          isActive: initialData.isActive,
        }
      : {
          serverName: '',
          host: '',
          port: 587,
          username: '',
          password: '',
          useSsl: false,
          useStartTls: true,
          additionalSettings: '',
          isActive: true,
        };
  }, [initialData]);

  // Memoize handlers để tránh re-render
  const handleFormSubmit = useCallback(
    (values: Record<string, unknown>) => {
      console.log('📝 Form submit called', { values });
      // Parse additionalSettings từ JSON string nếu có
      const processedValues = {
        ...values,
        additionalSettings: values.additionalSettings
          ? JSON.parse(values.additionalSettings as string)
          : undefined,
      };
      onSubmit(processedValues);
    },
    [onSubmit]
  );

  // Xử lý test kết nối trực tiếp
  const handleTestConnection = useCallback(async () => {
    console.log('🔗 Test connection clicked');

    if (!testEmail) {
      alert(t('integration:email.testEmailRequired', 'Vui lòng nhập email để test'));
      return;
    }

    // Validate email format
    if (!isValidEmail(testEmail)) {
      alert(t('integration:email.invalidEmailFormat', 'Định dạng email không hợp lệ'));
      return;
    }

    // Lấy dữ liệu form hiện tại
    const formData = formRef.current
      ? (formRef.current as { getValues: () => EmailServerFormValues }).getValues()
      : ({} as EmailServerFormValues);
    console.log('📋 Current form data:', formData);

    // Kiểm tra các trường bắt buộc
    if (!formData.host || !formData.username || !formData.password) {
      alert(
        t(
          'integration:email.fillRequiredFields',
          'Vui lòng điền đầy đủ thông tin Host, Username và Password'
        )
      );
      return;
    }

    try {
      const testData = {
        recipientEmail: testEmail,
        subject: 'Test Email từ RedAI - Kiểm tra cấu hình',
      };

      if (isEditMode && initialData?.id) {
        // Nếu đang chỉnh sửa, test với server hiện tại
        const testResponse = await testEmailMutation.mutateAsync({
          id: initialData.id,
          data: testData,
        });

        setTestResult({
          success: testResponse.result.success,
          message: testResponse.result.message || 'Test email đã được gửi thành công!',
        });
      } else {
        // Nếu đang tạo mới, sử dụng API test-with-config
        let additionalSettings = {};
        try {
          if (formData.additionalSettings && formData.additionalSettings.trim()) {
            additionalSettings = JSON.parse(formData.additionalSettings);
          }
        } catch (error) {
          console.warn('Invalid JSON in additionalSettings, using empty object:', error);
        }

        const emailServerConfig: EmailServerConfigDto = {
          serverName: formData.serverName || 'Test Configuration',
          host: formData.host,
          port: formData.port || 587,
          username: formData.username,
          password: formData.password,
          useSsl: formData.useSsl !== undefined ? formData.useSsl : false,
          useStartTls: formData.useStartTls !== undefined ? formData.useStartTls : true,
          additionalSettings,
        };

        const testEmailServerWithConfigData: TestEmailServerWithConfigDto = {
          emailServerConfig,
          testInfo: testData,
        };

        console.log('📤 Sending test request:', testEmailServerWithConfigData);
        const testResponse = await testEmailWithConfigMutation.mutateAsync(
          testEmailServerWithConfigData
        );

        setTestResult({
          success: testResponse.result.success,
          message: testResponse.result.message || 'Test email đã được gửi thành công!',
        });
      }
    } catch (error: unknown) {
      console.error('Error testing connection:', error);
      setTestResult({
        success: false,
        message:
          (error as { response?: { data?: { message?: string } } })?.response?.data?.message ||
          t('integration:email.testError', 'Lỗi khi kiểm tra kết nối'),
      });
    }
  }, [
    testEmail,
    t,
    isEditMode,
    initialData,
    testEmailMutation,
    testEmailWithConfigMutation,
    isValidEmail,
  ]);

  return (
    <Card
      title={
        readOnly
          ? t('admin:integration.email.form.view', 'Xem Email Server')
          : isEditMode
            ? t('admin:integration.email.form.edit', 'Chỉnh sửa Email Server')
            : t('admin:integration.email.form.create', 'Tạo Email Server')
      }
    >
      {/* Provider Selection Section */}
      {showProviderSelection && !isEditMode && (
        <div className="mb-6">
          <div className="mb-4">
            <Typography variant="h6" className="mb-2">
              {t('integration:wizard.selectProvider.title', 'Chọn nhà cung cấp email')}
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {t(
                'integration:wizard.selectProvider.description',
                'Chọn nhà cung cấp email để tự động cấu hình các thông số kết nối'
              )}
            </Typography>
          </div>

          {/* Search and Filters */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div>
              <Typography variant="caption" className="mb-2">
                {t('common:search')}
              </Typography>
              <Input
                placeholder={t(
                  'integration:wizard.selectProvider.searchPlaceholder',
                  'Tìm kiếm nhà cung cấp...'
                )}
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                leftIcon={<Icon name="search" size="sm" />}
                fullWidth
              />
            </div>

            <div>
              <Typography variant="caption" className="mb-2">
                {t('integration:provider.category.title', 'Danh mục')}
              </Typography>
              <div className="flex flex-wrap gap-2">
                <Chip
                  variant={selectedCategory === 'all' ? 'primary' : 'default'}
                  outlined={selectedCategory !== 'all'}
                  className="cursor-pointer"
                  onClick={() => setSelectedCategory('all')}
                >
                  {t('integration:provider.category.all', 'Tất cả')}
                </Chip>
                <Chip
                  variant={selectedCategory === 'personal' ? 'primary' : 'default'}
                  outlined={selectedCategory !== 'personal'}
                  className="cursor-pointer"
                  onClick={() => setSelectedCategory('personal')}
                >
                  {t('integration:provider.category.personal', 'Cá nhân')}
                </Chip>
                <Chip
                  variant={selectedCategory === 'business' ? 'primary' : 'default'}
                  outlined={selectedCategory !== 'business'}
                  className="cursor-pointer"
                  onClick={() => setSelectedCategory('business')}
                >
                  {t('integration:provider.category.business', 'Doanh nghiệp')}
                </Chip>
              </div>
            </div>

            <div className="flex items-end">
              <Button
                variant="outline"
                onClick={() => setShowProviderSelection(false)}
                className="w-full"
              >
                <Icon name="settings" className="w-4 h-4 mr-2" />
                {t('integration:wizard.selectProvider.customConfig', 'Cấu hình thủ công')}
              </Button>
            </div>
          </div>

          {/* Provider Grid */}
          {isLoadingProviders ? (
            <div className="flex justify-center py-8">
              <Icon name="loader" className="w-6 h-6 animate-spin" />
            </div>
          ) : (
            <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 3, lg: 4 }}>
              {filteredProviders.map(provider => (
                <ProviderCard
                  key={provider.id}
                  provider={provider}
                  isSelected={false}
                  onClick={() => handleProviderSelect(provider)}
                />
              ))}
            </ResponsiveGrid>
          )}

          {filteredProviders.length === 0 && !isLoadingProviders && (
            <div className="text-center py-8">
              <Typography variant="body2" className="text-muted-foreground">
                {t(
                  'integration:wizard.selectProvider.noResults',
                  'Không tìm thấy nhà cung cấp nào'
                )}
              </Typography>
            </div>
          )}
        </div>
      )}

      {/* Selected Provider Header */}
      {selectedProvider && !showProviderSelection && (
        <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 flex items-center justify-center bg-white rounded">
                {selectedProvider.logoUrl ? (
                  <img
                    src={selectedProvider.logoUrl}
                    alt={selectedProvider.displayName}
                    className="w-6 h-6 object-contain"
                  />
                ) : (
                  <Icon name="mail" className="w-4 h-4 text-gray-500" />
                )}
              </div>
              <div>
                <Typography variant="subtitle1">{selectedProvider.displayName}</Typography>
                <Typography variant="caption" className="text-muted-foreground">
                  {selectedProvider.description}
                </Typography>
              </div>
            </div>
            <Button variant="outline" size="sm" onClick={handleBackToProviderSelection}>
              <Icon name="arrow-left" className="w-4 h-4 mr-2" />
              {t('integration:wizard.selectProvider.changeProvider', 'Đổi nhà cung cấp')}
            </Button>
          </div>
        </div>
      )}

      {/* Form Section */}
      {(!showProviderSelection || isEditMode) && (
        <Form
          schema={emailServerConfigurationSchema}
          onSubmit={handleFormSubmit}
          className="space-y-6"
          defaultValues={defaultValues}
          ref={formRef}
          useDefaultValuesOnce={true}
        >
          <FormItem
            name="serverName"
            label={t('admin:integration.email.form.fields.serverName')}
            required
          >
            <Input
              placeholder={t('admin:integration.email.form.placeholders.serverName')}
              disabled={readOnly || isSubmitting}
              fullWidth
            />
          </FormItem>

          <FormItem name="host" label={t('admin:integration.email.form.fields.host')} required>
            <Input
              placeholder={t('admin:integration.email.form.placeholders.host')}
              disabled={readOnly || isSubmitting}
              fullWidth
            />
          </FormItem>

          <FormItem name="port" label={t('admin:integration.email.form.fields.port')} required>
            <Input
              type="number"
              placeholder={t('admin:integration.email.form.placeholders.port')}
              disabled={readOnly || isSubmitting}
              fullWidth
            />
          </FormItem>

          {/* Provider-specific or manual fields */}
          {selectedProvider ? (
            <div>
              <Typography variant="h6" className="mb-4">
                {t('integration:provider.credentials', 'Thông tin xác thực')}
              </Typography>
              {getProviderSpecificFields()}
            </div>
          ) : (
            <>
              <FormItem
                name="username"
                label={t('admin:integration.email.form.fields.username')}
                required
              >
                <Input
                  type="email"
                  placeholder={t('admin:integration.email.form.placeholders.username')}
                  disabled={readOnly || isSubmitting}
                  fullWidth
                />
              </FormItem>

              <FormItem
                name="password"
                label={t('admin:integration.email.form.fields.password')}
                required={!isEditMode}
              >
                <Input
                  type="password"
                  placeholder={t('admin:integration.email.form.placeholders.password')}
                  disabled={readOnly || isSubmitting}
                  fullWidth
                />
              </FormItem>
            </>
          )}

          {/* SSL/TLS Settings */}
          <div className="flex space-x-8">
            <FormItem name="useSsl" label={t('admin:integration.email.form.fields.useSsl')}>
              <Toggle disabled={readOnly || isSubmitting} label="" />
            </FormItem>

            <FormItem
              name="useStartTls"
              label={t('admin:integration.email.form.fields.useStartTls')}
            >
              <Toggle disabled={readOnly || isSubmitting} label="" />
            </FormItem>

            <FormItem name="isActive" label={t('admin:integration.email.form.fields.isActive')}>
              <Toggle disabled={readOnly || isSubmitting} label="" />
            </FormItem>
          </div>

          {/* Additional Settings */}
          <FormItem
            name="additionalSettings"
            label={t('admin:integration.email.form.fields.additionalSettings')}
          >
            <Textarea
              placeholder={t('admin:integration.email.form.placeholders.additionalSettings')}
              disabled={readOnly || isSubmitting}
              rows={4}
              fullWidth
            />
          </FormItem>

          {/* Test Connection Section */}
          {!readOnly && (
            <div>
              <Typography variant="h6" className="mb-4">
                {t('admin:integration.email.form.test', 'Kiểm tra kết nối')}
              </Typography>

              <Typography variant="body2" className="text-gray-600 dark:text-gray-400 mb-4">
                {t(
                  'admin:integration.email.form.testEmailDescription',
                  'Nhập email để nhận email test từ cấu hình này'
                )}
              </Typography>

              <div className="flex space-x-4">
                <div className="flex-1">
                  <Input
                    type="email"
                    placeholder="<EMAIL>"
                    value={testEmail}
                    onChange={e => setTestEmail(e.target.value)}
                    leftIcon={<Icon name="mail" size="sm" />}
                    fullWidth
                    error={
                      testEmail && !isValidEmail(testEmail)
                        ? t('integration:email.invalidEmailFormat', 'Định dạng email không hợp lệ')
                        : undefined
                    }
                  />
                </div>
                <Button
                  type="button"
                  variant="primary"
                  leftIcon={<Icon name="send" size="sm" />}
                  onClick={handleTestConnection}
                  disabled={
                    !testEmail ||
                    !isValidEmail(testEmail) ||
                    testEmailMutation.isPending ||
                    testEmailWithConfigMutation.isPending
                  }
                  isLoading={testEmailMutation.isPending || testEmailWithConfigMutation.isPending}
                >
                  {t('admin:integration.email.actions.sendTest', 'Gửi test')}
                </Button>
              </div>

              {/* Test Result */}
              {testResult && (
                <div className="mt-4">
                  <Alert
                    type={testResult.success ? 'success' : 'error'}
                    title={testResult.success ? t('common:success') : t('common:error')}
                    message={testResult.message}
                    showIcon
                  />
                </div>
              )}
            </div>
          )}

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-4">
            <Button variant="outline" onClick={onCancel} disabled={isSubmitting}>
              {t('admin:integration.email.actions.cancel')}
            </Button>
            {!readOnly && (
              <Button type="submit" variant="primary" isLoading={isSubmitting}>
                {t('admin:integration.email.actions.save')}
              </Button>
            )}
          </div>
        </Form>
      )}
    </Card>
  );
};

export default EmailServerForm;
