import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Icon, Chip } from '@/shared/components/common';
import { EmailProvider } from '../../types/providers';

interface ProviderCardProps {
  provider: EmailProvider;
  isSelected?: boolean;
  onClick?: (provider: EmailProvider) => void;
  className?: string;
}

/**
 * ProviderCard Component
 * Hiển thị thông tin email provider trong dạng card
 */
const ProviderCard: React.FC<ProviderCardProps> = ({
  provider,
  isSelected = false,
  onClick,
  className = '',
}) => {
  const { t } = useTranslation(['integration', 'common']);

  const handleClick = () => {
    if (onClick) {
      onClick(provider);
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'personal':
        return 'user';
      case 'business':
        return 'building';
      case 'transactional':
        return 'send';
      default:
        return 'mail';
    }
  };

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'personal':
        return t('integration:provider.category.personal');
      case 'business':
        return t('integration:provider.category.business');
      case 'transactional':
        return t('integration:provider.category.transactional');
      default:
        return category;
    }
  };



  return (
    <Card
      className={`
        cursor-pointer transition-all duration-200 hover:shadow-lg
        ${isSelected ? 'ring-2 ring-primary border-primary' : 'border-border hover:border-primary/50'}
        ${className}
      `}
      onClick={handleClick}
    >
      <div className="p-4">
        {/* Header với logo và tên */}
        <div className="flex items-center space-x-3 mb-3">
          {/* Provider Logo */}
          <div className="w-10 h-10 flex items-center justify-center bg-gray-100 rounded-lg">
            {provider.logoUrl ? (
              <img
                src={provider.logoUrl}
                alt={provider.displayName}
                className="w-8 h-8 object-contain"
              />
            ) : (
              <Icon name="mail" className="w-6 h-6 text-gray-500" />
            )}
          </div>

          {/* Provider Name */}
          <div>
            <Typography variant="h6" className="font-semibold text-foreground">
              {provider.displayName}
            </Typography>
            <div className="flex items-center space-x-2 mt-1">
              <Icon
                name={getCategoryIcon(provider.category)}
                className="w-3 h-3 text-muted-foreground"
              />
              <Typography variant="caption" className="text-muted-foreground">
                {getCategoryLabel(provider.category)}
              </Typography>
            </div>
          </div>
        </div>

        {/* Description */}
        <Typography variant="body2" className="text-muted-foreground mb-3 line-clamp-2">
          {provider.description}
        </Typography>

        {/* Features */}
        {provider.features && provider.features.length > 0 && (
          <div className="mb-3">
            <Typography variant="caption" className="text-muted-foreground font-medium mb-1 block">
              {t('integration:provider.features')}:
            </Typography>
            <div className="flex flex-wrap gap-1">
              {provider.features.slice(0, 3).map((feature, index) => (
                <Chip key={index} outlined size="sm">
                  {feature}
                </Chip>
              ))}
              {provider.features.length > 3 && (
                <Chip outlined size="sm">
                  +{provider.features.length - 3} {t('common:more')}
                </Chip>
              )}
            </div>
          </div>
        )}

        {/* Auth Methods */}
        <div className="mb-3">
          <Typography variant="caption" className="text-muted-foreground font-medium mb-1 block">
            {t('integration:provider.authMethods')}:
          </Typography>
          <div className="flex space-x-2">
            {provider.authMethods.map((method) => (
              <div key={method} className="flex items-center space-x-1">
                <Icon 
                  name={method === 'oauth' ? 'shield' : method === 'apikey' ? 'code' : 'key'} 
                  className="w-3 h-3 text-muted-foreground" 
                />
                <Typography variant="caption" className="text-muted-foreground">
                  {method === 'oauth' ? 'OAuth' : method === 'apikey' ? 'API Key' : 'Password'}
                </Typography>
              </div>
            ))}
          </div>
        </div>



        {/* Selection Indicator */}
        {isSelected && (
          <div className="absolute top-2 right-2">
            <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center">
              <Icon name="check" className="w-4 h-4 text-white" />
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

export default ProviderCard;
